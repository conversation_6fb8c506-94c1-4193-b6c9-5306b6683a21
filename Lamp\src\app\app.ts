import { Component, signal } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { Controller } from "./controller/controller";

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, MatSlideToggleModule, Controller],
  templateUrl: './app.html',
  styleUrl: './app.scss'
})
export class App {
  protected readonly title = signal('Lamp');
}
